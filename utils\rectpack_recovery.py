#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法错误恢复机制
提供自动错误检测、恢复和降级策略，确保生产环境的稳定性
"""

import os
import sys
import logging
import traceback
from typing import Dict, Any, List, Optional, Callable
from enum import Enum

# 导入统一的时间处理工具
from utils.time_helper import get_timestamp, safe_sleep

# 配置日志
from utils.log_config import get_logger
log = get_logger("RectPackRecovery")

class RecoveryStrategy(Enum):
    """恢复策略枚举"""
    RETRY = "retry"  # 重试
    FALLBACK = "fallback"  # 降级
    SKIP = "skip"  # 跳过
    ABORT = "abort"  # 中止

class ErrorType(Enum):
    """错误类型枚举"""
    IMPORT_ERROR = "import_error"  # 导入错误
    ALGORITHM_ERROR = "algorithm_error"  # 算法错误
    MEMORY_ERROR = "memory_error"  # 内存错误
    TIMEOUT_ERROR = "timeout_error"  # 超时错误
    PHOTOSHOP_ERROR = "photoshop_error"  # Photoshop错误
    UNKNOWN_ERROR = "unknown_error"  # 未知错误

class RectPackRecoveryManager:
    """RectPack错误恢复管理器"""

    def __init__(self, log_signal: Optional[Callable] = None):
        """
        初始化恢复管理器

        Args:
            log_signal: 日志信号回调函数
        """
        self.log_signal = log_signal
        self.error_counts = {}  # 错误计数
        self.recovery_history = []  # 恢复历史
        self.max_retries = 3  # 最大重试次数
        self.fallback_enabled = True  # 是否启用降级

        # 错误恢复策略映射
        self.recovery_strategies = {
            ErrorType.IMPORT_ERROR: RecoveryStrategy.FALLBACK,
            ErrorType.ALGORITHM_ERROR: RecoveryStrategy.RETRY,
            ErrorType.MEMORY_ERROR: RecoveryStrategy.FALLBACK,
            ErrorType.TIMEOUT_ERROR: RecoveryStrategy.RETRY,
            ErrorType.PHOTOSHOP_ERROR: RecoveryStrategy.RETRY,
            ErrorType.UNKNOWN_ERROR: RecoveryStrategy.RETRY,
        }

    def log(self, message: str):
        """记录日志"""
        if self.log_signal:
            self.log_signal(message)
        log.info(message)

    def classify_error(self, error: Exception) -> ErrorType:
        """
        分类错误类型

        Args:
            error: 异常对象

        Returns:
            ErrorType: 错误类型
        """
        error_str = str(error).lower()
        error_type_name = type(error).__name__.lower()

        if isinstance(error, ImportError) or "import" in error_str:
            return ErrorType.IMPORT_ERROR
        elif isinstance(error, MemoryError) or "memory" in error_str or "out of memory" in error_str:
            return ErrorType.MEMORY_ERROR
        elif isinstance(error, TimeoutError) or "timeout" in error_str or "time out" in error_str:
            return ErrorType.TIMEOUT_ERROR
        elif "photoshop" in error_str or "ps" in error_str or "com" in error_str:
            return ErrorType.PHOTOSHOP_ERROR
        elif "rectpack" in error_str or "pack" in error_str or "bin" in error_str:
            return ErrorType.ALGORITHM_ERROR
        else:
            return ErrorType.UNKNOWN_ERROR

    def should_retry(self, error_type: ErrorType, error_count: int) -> bool:
        """
        判断是否应该重试

        Args:
            error_type: 错误类型
            error_count: 错误次数

        Returns:
            bool: 是否应该重试
        """
        strategy = self.recovery_strategies.get(error_type, RecoveryStrategy.RETRY)

        if strategy == RecoveryStrategy.RETRY:
            return error_count < self.max_retries
        elif strategy == RecoveryStrategy.FALLBACK:
            return error_count < 1  # 降级策略只重试一次
        else:
            return False

    def handle_error(self, error: Exception, context: str = "") -> RecoveryStrategy:
        """
        处理错误并返回恢复策略

        Args:
            error: 异常对象
            context: 错误上下文

        Returns:
            RecoveryStrategy: 推荐的恢复策略
        """
        error_type = self.classify_error(error)
        error_key = f"{error_type.value}_{context}"

        # 更新错误计数
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        error_count = self.error_counts[error_key]

        # 记录错误
        self.log(f"❌ 错误类型: {error_type.value}, 上下文: {context}, 次数: {error_count}")
        self.log(f"   错误详情: {str(error)}")

        # 确定恢复策略
        strategy = self.recovery_strategies.get(error_type, RecoveryStrategy.RETRY)

        # 检查是否应该重试
        if strategy == RecoveryStrategy.RETRY and not self.should_retry(error_type, error_count):
            strategy = RecoveryStrategy.FALLBACK if self.fallback_enabled else RecoveryStrategy.ABORT

        # 记录恢复历史
        recovery_record = {
            'timestamp': get_timestamp(),
            'error_type': error_type.value,
            'context': context,
            'error_count': error_count,
            'strategy': strategy.value,
            'error_message': str(error)
        }
        self.recovery_history.append(recovery_record)

        self.log(f"🔧 恢复策略: {strategy.value}")

        return strategy

    def execute_recovery(self, strategy: RecoveryStrategy,
                        retry_func: Optional[Callable] = None,
                        fallback_func: Optional[Callable] = None) -> Any:
        """
        执行恢复策略

        Args:
            strategy: 恢复策略
            retry_func: 重试函数
            fallback_func: 降级函数

        Returns:
            Any: 执行结果
        """
        if strategy == RecoveryStrategy.RETRY:
            if retry_func:
                self.log("🔄 执行重试策略...")
                safe_sleep(1)  # 短暂等待
                return retry_func()
            else:
                self.log("⚠️ 重试策略无可用函数")
                return None

        elif strategy == RecoveryStrategy.FALLBACK:
            if fallback_func:
                self.log("⬇️ 执行降级策略...")
                return fallback_func()
            else:
                self.log("⚠️ 降级策略无可用函数")
                return None

        elif strategy == RecoveryStrategy.SKIP:
            self.log("⏭️ 跳过当前操作")
            return None

        elif strategy == RecoveryStrategy.ABORT:
            self.log("🛑 中止操作")
            raise RuntimeError("操作已中止")

        return None

    def with_recovery(self, func: Callable, context: str = "",
                     fallback_func: Optional[Callable] = None,
                     max_attempts: int = 3) -> Any:
        """
        带恢复机制的函数执行装饰器

        Args:
            func: 要执行的函数
            context: 执行上下文
            fallback_func: 降级函数
            max_attempts: 最大尝试次数

        Returns:
            Any: 执行结果
        """
        last_error = None

        for attempt in range(max_attempts):
            try:
                if attempt > 0:
                    self.log(f"🔄 第 {attempt + 1} 次尝试执行: {context}")

                result = func()

                if attempt > 0:
                    self.log(f"✅ 重试成功: {context}")

                return result

            except Exception as e:
                last_error = e

                # 处理错误并获取恢复策略
                strategy = self.handle_error(e, context)

                # 如果是最后一次尝试，或者策略是中止/降级
                if (attempt == max_attempts - 1 or
                    strategy in [RecoveryStrategy.ABORT, RecoveryStrategy.FALLBACK]):

                    if strategy == RecoveryStrategy.FALLBACK and fallback_func:
                        self.log(f"⬇️ 执行降级策略: {context}")
                        try:
                            return fallback_func()
                        except Exception as fallback_error:
                            self.log(f"❌ 降级策略也失败: {str(fallback_error)}")
                            raise last_error
                    elif strategy == RecoveryStrategy.ABORT:
                        raise last_error
                    else:
                        # 最后一次尝试失败
                        break

                # 继续重试
                if strategy == RecoveryStrategy.RETRY:
                    continue
                else:
                    break

        # 所有尝试都失败了
        self.log(f"❌ 所有尝试都失败: {context}")
        raise last_error

    def get_recovery_stats(self) -> Dict[str, Any]:
        """
        获取恢复统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        total_errors = sum(self.error_counts.values())
        error_types = {}

        for error_key, count in self.error_counts.items():
            error_type = error_key.split('_')[0]
            error_types[error_type] = error_types.get(error_type, 0) + count

        current_time = get_timestamp()
        recent_recoveries = [r for r in self.recovery_history
                           if current_time - r['timestamp'] < 3600]  # 最近1小时

        return {
            'total_errors': total_errors,
            'error_types': error_types,
            'recent_recoveries': len(recent_recoveries),
            'recovery_history_count': len(self.recovery_history),
            'error_counts': self.error_counts.copy()
        }

    def reset_stats(self):
        """重置统计信息"""
        self.error_counts.clear()
        self.recovery_history.clear()
        self.log("📊 恢复统计信息已重置")


# 全局恢复管理器实例
_global_recovery_manager = None

def get_recovery_manager(log_signal: Optional[Callable] = None) -> RectPackRecoveryManager:
    """
    获取全局恢复管理器实例

    Args:
        log_signal: 日志信号回调函数

    Returns:
        RectPackRecoveryManager: 恢复管理器实例
    """
    global _global_recovery_manager

    if _global_recovery_manager is None:
        _global_recovery_manager = RectPackRecoveryManager(log_signal)
    elif log_signal and not _global_recovery_manager.log_signal:
        _global_recovery_manager.log_signal = log_signal

    return _global_recovery_manager


def with_rectpack_recovery(context: str = "", fallback_func: Optional[Callable] = None):
    """
    RectPack恢复装饰器

    Args:
        context: 执行上下文
        fallback_func: 降级函数

    Returns:
        装饰器函数
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            recovery_manager = get_recovery_manager()

            def execute_func():
                return func(*args, **kwargs)

            return recovery_manager.with_recovery(
                execute_func, context, fallback_func
            )

        return wrapper
    return decorator


if __name__ == "__main__":
    # 测试代码
    recovery_manager = RectPackRecoveryManager()

    # 模拟不同类型的错误
    test_errors = [
        ImportError("No module named 'rectpack'"),
        MemoryError("Out of memory"),
        TimeoutError("Operation timed out"),
        Exception("Photoshop connection failed"),
        Exception("Unknown error")
    ]

    for error in test_errors:
        strategy = recovery_manager.handle_error(error, "test_context")
        print(f"错误: {error}, 策略: {strategy.value}")

    # 显示统计信息
    stats = recovery_manager.get_recovery_stats()
    print(f"\n统计信息: {stats}")
