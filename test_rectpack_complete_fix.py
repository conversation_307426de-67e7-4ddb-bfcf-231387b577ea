#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法完整修复验证脚本
彻底验证生产模式的两个核心问题是否已修复：
1. 生产模式是否使用了与测试模式一致的参数
2. 生产模式是否生成TXT说明文档
"""

import logging
import sys
import os
import tempfile
from typing import Dict, Any, List

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

def test_unified_params_in_production():
    """测试生产模式是否使用统一参数"""
    log.info("=== 测试生产模式统一参数 ===")
    
    try:
        from core.unified_image_arranger import UnifiedImageArranger
        from core.rectpack_params import unified_params
        
        # 创建统一排列器（模拟生产模式）
        arranger = UnifiedImageArranger(log_signal=None)
        arranger.initialize(
            canvas_width_px=200,
            max_height_px=300,
            image_spacing_px=1,
            ppi=72,
            is_test_mode=False  # 生产模式
        )
        
        # 检查RectPack排列器是否使用了统一参数
        rectpack_arranger = arranger.rectpack_arranger
        
        expected_rotation = unified_params.rotation_enabled
        actual_rotation = rectpack_arranger.rotation_enabled
        
        expected_spacing = unified_params.spacing_px
        actual_spacing = rectpack_arranger.image_spacing
        
        log.info(f"期望旋转设置: {expected_rotation}")
        log.info(f"实际旋转设置: {actual_rotation}")
        log.info(f"期望间距设置: {expected_spacing}px")
        log.info(f"实际间距设置: {actual_spacing}px")
        
        # 验证参数一致性
        params_match = (expected_rotation == actual_rotation and 
                       expected_spacing == actual_spacing)
        
        if params_match:
            log.info("✅ 生产模式成功使用统一参数")
            
            # 测试排序函数
            sort_func = rectpack_arranger._create_optimized_sort_function()
            test_rects = [(120, 60), (80, 50), (100, 80)]
            sorted_rects = sort_func(test_rects)
            
            # 验证排序是否按面积降序
            areas = [rect[0] * rect[1] for rect in sorted_rects]
            is_descending = all(areas[i] >= areas[i+1] for i in range(len(areas)-1))
            
            if is_descending:
                log.info("✅ 生产模式使用统一的排序函数")
                return True
            else:
                log.error("❌ 生产模式排序函数不一致")
                return False
        else:
            log.error("❌ 生产模式未使用统一参数")
            return False
            
    except Exception as e:
        log.error(f"❌ 生产模式统一参数测试失败: {str(e)}")
        return False

def test_production_txt_generation():
    """测试生产模式TXT文档生成"""
    log.info("=== 测试生产模式TXT文档生成 ===")
    
    try:
        from core.rectpack_txt_generator import txt_generator
        
        # 模拟生产模式的图片数据
        test_images = [
            {
                'name': 'ProductionImage_1',
                'width': 120,
                'height': 60,
                'x': 0,
                'y': 0,
                'rotated': False,
                'need_rotation': False
            },
            {
                'name': 'ProductionImage_2',
                'width': 80,
                'height': 50,
                'x': 120,
                'y': 0,
                'rotated': True,
                'need_rotation': True
            },
            {
                'name': 'ProductionImage_3',
                'width': 100,
                'height': 80,
                'x': 0,
                'y': 60,
                'rotated': False,
                'need_rotation': False
            }
        ]
        
        # 创建临时文档路径
        with tempfile.NamedTemporaryFile(mode='w', suffix='_说明.txt', delete=False) as tmp_file:
            test_doc_path = tmp_file.name
        
        # 生成生产模式TXT文档
        success = txt_generator.generate_production_txt_doc(
            doc_path=test_doc_path,
            arranged_images=test_images,
            canvas_width_px=200,
            canvas_height_px=140,
            material_name="生产测试材质",
            canvas_sequence=1,
            ppi=72
        )
        
        if success and os.path.exists(test_doc_path):
            log.info("✅ 生产模式TXT文档生成成功")
            
            # 检查文档内容
            with open(test_doc_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 验证关键内容
            required_sections = [
                "RectPack算法生产模式报告",
                "★ 基本信息",
                "★ 容器详情", 
                "★ 布局统计",
                "★ 详细图片信息",
                "★ 技术说明",
                "★ 算法参数",
                "★ 性能统计",
                "★ 输出信息"
            ]
            
            missing_sections = []
            for section in required_sections:
                if section not in content:
                    missing_sections.append(section)
            
            if not missing_sections:
                log.info("✅ 生产模式TXT文档包含所有必需的章节")
                
                # 检查图片信息表格
                image_names = [img['name'] for img in test_images]
                found_images = [name for name in image_names if name in content]
                
                if len(found_images) == len(image_names):
                    log.info("✅ 生产模式TXT文档包含所有图片详细信息")
                    
                    # 检查生产模式特有内容
                    production_keywords = [
                        "生产模式: 启用 (Photoshop集成)",
                        "cm转px (生产模式",
                        "Photoshop集成",
                        "TIFF输出"
                    ]
                    
                    found_keywords = [kw for kw in production_keywords if kw in content]
                    
                    if len(found_keywords) >= 3:
                        log.info("✅ 生产模式TXT文档包含生产模式特有内容")
                        
                        # 清理测试文件
                        os.unlink(test_doc_path)
                        return True
                    else:
                        log.error(f"❌ 生产模式TXT文档缺少生产模式特有内容: {set(production_keywords) - set(found_keywords)}")
                        return False
                else:
                    log.error(f"❌ 生产模式TXT文档缺少图片信息: {set(image_names) - set(found_images)}")
                    return False
            else:
                log.error(f"❌ 生产模式TXT文档缺少章节: {missing_sections}")
                return False
        else:
            log.error("❌ 生产模式TXT文档生成失败")
            return False
            
    except Exception as e:
        log.error(f"❌ 生产模式TXT文档生成测试失败: {str(e)}")
        return False

def test_rectpack_layout_worker_integration():
    """测试RectPackLayoutWorker集成"""
    log.info("=== 测试RectPackLayoutWorker集成 ===")
    
    try:
        # 检查RectPackLayoutWorker是否能正确导入所有修复的模块
        from ui.rectpack_layout_worker import RectPackLayoutWorker
        
        log.info("✅ RectPackLayoutWorker导入成功")
        
        # 检查是否能正确导入统一参数
        try:
            from core.rectpack_params import unified_params
            log.info("✅ RectPackLayoutWorker可以访问统一参数")
        except ImportError as e:
            log.error(f"❌ RectPackLayoutWorker无法访问统一参数: {str(e)}")
            return False
        
        # 检查是否能正确导入TXT生成器
        try:
            from core.rectpack_txt_generator import txt_generator
            log.info("✅ RectPackLayoutWorker可以访问TXT生成器")
        except ImportError as e:
            log.error(f"❌ RectPackLayoutWorker无法访问TXT生成器: {str(e)}")
            return False
        
        # 检查是否能正确导入统一排列器
        try:
            from core.unified_image_arranger import UnifiedImageArranger
            log.info("✅ RectPackLayoutWorker可以访问统一排列器")
        except ImportError as e:
            log.error(f"❌ RectPackLayoutWorker无法访问统一排列器: {str(e)}")
            return False
        
        return True
        
    except Exception as e:
        log.error(f"❌ RectPackLayoutWorker集成测试失败: {str(e)}")
        return False

def test_end_to_end_consistency():
    """测试端到端一致性"""
    log.info("=== 测试端到端一致性 ===")
    
    try:
        from core.rectpack_params import unified_params
        from core.unified_image_arranger import UnifiedImageArranger
        from core.rectpack_txt_generator import txt_generator
        
        # 模拟相同的图片数据
        test_patterns = [
            {'pattern_name': 'TestPattern_1', 'width_cm': 12, 'height_cm': 6, 'path': 'test1.jpg'},
            {'pattern_name': 'TestPattern_2', 'width_cm': 8, 'height_cm': 5, 'path': 'test2.jpg'},
            {'pattern_name': 'TestPattern_3', 'width_cm': 10, 'height_cm': 8, 'path': 'test3.jpg'}
        ]
        
        # 测试模式排列
        test_arranger = UnifiedImageArranger(log_signal=None)
        test_arranger.initialize(
            canvas_width_px=200,
            max_height_px=300,
            image_spacing_px=1,
            ppi=72,
            is_test_mode=True  # 测试模式
        )
        
        test_arranged = test_arranger.arrange_images(test_patterns.copy())
        test_stats = test_arranger.get_layout_statistics()
        
        # 生产模式排列
        prod_arranger = UnifiedImageArranger(log_signal=None)
        prod_arranger.initialize(
            canvas_width_px=200,
            max_height_px=300,
            image_spacing_px=1,
            ppi=72,
            is_test_mode=False  # 生产模式
        )
        
        prod_arranged = prod_arranger.arrange_images(test_patterns.copy())
        prod_stats = prod_arranger.get_layout_statistics()
        
        # 比较结果
        test_count = len(test_arranged)
        prod_count = len(prod_arranged)
        
        test_utilization = test_stats.get('utilization_percent', 0)
        prod_utilization = prod_stats.get('utilization_percent', 0)
        
        log.info(f"测试模式: {test_count}张图片, 利用率: {test_utilization:.2f}%")
        log.info(f"生产模式: {prod_count}张图片, 利用率: {prod_utilization:.2f}%")
        
        # 验证一致性（允许小幅差异）
        count_consistent = test_count == prod_count
        utilization_consistent = abs(test_utilization - prod_utilization) < 5.0  # 允许5%差异
        
        if count_consistent and utilization_consistent:
            log.info("✅ 测试模式和生产模式结果基本一致")
            
            # 测试TXT文档生成
            with tempfile.NamedTemporaryFile(mode='w', suffix='_说明.txt', delete=False) as tmp_file:
                test_doc_path = tmp_file.name
            
            txt_success = txt_generator.generate_production_txt_doc(
                doc_path=test_doc_path,
                arranged_images=prod_arranged,
                canvas_width_px=200,
                canvas_height_px=300,
                material_name="端到端测试",
                canvas_sequence=1,
                ppi=72
            )
            
            if txt_success:
                log.info("✅ 端到端TXT文档生成成功")
                os.unlink(test_doc_path)
                return True
            else:
                log.error("❌ 端到端TXT文档生成失败")
                return False
        else:
            log.error(f"❌ 测试模式和生产模式结果不一致")
            log.error(f"   图片数量一致: {count_consistent}")
            log.error(f"   利用率一致: {utilization_consistent}")
            return False
            
    except Exception as e:
        log.error(f"❌ 端到端一致性测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    log.info("开始RectPack算法完整修复验证")
    log.info("=" * 60)
    
    tests = [
        ("生产模式统一参数", test_unified_params_in_production),
        ("生产模式TXT文档生成", test_production_txt_generation),
        ("RectPackLayoutWorker集成", test_rectpack_layout_worker_integration),
        ("端到端一致性", test_end_to_end_consistency),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        log.info(f"\n--- 测试 {test_name} ---")
        try:
            if test_func():
                passed += 1
                log.info(f"✅ {test_name} 测试通过")
            else:
                log.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            log.error(f"❌ {test_name} 测试异常: {str(e)}")
    
    log.info(f"\n" + "=" * 60)
    log.info(f"测试结果汇总")
    log.info(f"=" * 60)
    log.info(f"通过: {passed}/{total}")
    log.info(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        log.info("🎉 所有RectPack算法修复验证通过！")
        log.info("")
        log.info("核心问题修复状态:")
        log.info("  ✅ 问题1: 生产模式现在使用与测试模式一致的参数")
        log.info("  ✅ 问题2: 生产模式现在生成TXT说明文档")
        log.info("")
        log.info("修复效果:")
        log.info("  🎯 参数统一: 测试模式和生产模式使用相同的算法参数")
        log.info("  📄 文档完整: 生产模式生成与测试模式格式一致的TXT文档")
        log.info("  🔧 集成完整: 所有模块正确集成到RectPackLayoutWorker")
        log.info("  📊 结果一致: 测试模式和生产模式的布局结果基本一致")
        return True
    else:
        log.error("❌ 部分RectPack算法修复验证失败")
        log.error("请检查失败的测试项目并进行进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
