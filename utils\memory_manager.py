#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
内存管理器模块

提供内存优化和管理功能：
1. 内存使用监控
2. 惰性加载和流式处理
3. 内存回收和优化
4. 大型数据结构优化
"""

import os
import sys
import logging
import time
import gc
import weakref
import threading
import psutil
import numpy as np
from typing import List, Dict, Any, Optional, Tuple, Union, Callable
from utils.time_helper import get_timestamp, safe_sleep

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("MemoryManager")

class MemoryManager:
    """内存管理器，提供内存优化和管理功能"""

    # 单例模式
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """单例模式实现"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(MemoryManager, cls).__new__(cls)
                cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """初始化内存管理器"""
        # 避免重复初始化
        if self._initialized:
            return

        # 初始化成员变量
        self.memory_usage_history = []
        self.memory_thresholds = {
            'warning': 75,  # 内存使用率警告阈值（百分比）
            'critical': 90   # 内存使用率临界阈值（百分比）
        }
        self.auto_cleanup_enabled = True
        self.cleanup_interval = 60  # 自动清理间隔（秒）
        self.last_cleanup_time = 0
        self.cached_objects = {}  # 缓存对象字典
        self.cache_size_limit = 100 * 1024 * 1024  # 缓存大小限制（字节）
        self.current_cache_size = 0
        self.object_pools = {}  # 对象池字典

        # 创建监控线程
        self.monitor_thread = None
        self.monitor_running = False

        # 标记为已初始化
        self._initialized = True

        log.info("内存管理器初始化完成")

    def start_monitoring(self, interval: float = 5.0):
        """
        开始内存监控

        Args:
            interval: 监控间隔（秒）
        """
        if self.monitor_thread is not None and self.monitor_thread.is_alive():
            log.warning("内存监控已在运行")
            return

        self.monitor_running = True
        self.monitor_thread = threading.Thread(target=self._monitor_memory, args=(interval,), daemon=True)
        self.monitor_thread.start()
        log.info(f"内存监控已启动，间隔: {interval}秒")

    def stop_monitoring(self):
        """停止内存监控"""
        self.monitor_running = False
        if self.monitor_thread is not None:
            self.monitor_thread.join(timeout=1.0)
            self.monitor_thread = None
        log.info("内存监控已停止")

    def _monitor_memory(self, interval: float):
        """
        内存监控线程函数

        Args:
            interval: 监控间隔（秒）
        """
        while self.monitor_running:
            try:
                # 获取当前内存使用情况
                memory_info = self.get_memory_info()

                # 记录内存使用历史
                self.memory_usage_history.append({
                    'timestamp': get_timestamp(),
                    'usage_percent': memory_info['percent'],
                    'used': memory_info['used'],
                    'available': memory_info['available']
                })

                # 限制历史记录数量
                if len(self.memory_usage_history) > 1000:
                    self.memory_usage_history = self.memory_usage_history[-1000:]

                # 检查是否需要自动清理
                if self.auto_cleanup_enabled:
                    current_time = get_timestamp()
                    if (current_time - self.last_cleanup_time > self.cleanup_interval and
                        memory_info['percent'] > self.memory_thresholds['warning']):
                        self.cleanup_memory()
                        self.last_cleanup_time = current_time

                # 检查是否达到临界阈值
                if memory_info['percent'] > self.memory_thresholds['critical']:
                    log.warning(f"内存使用率达到临界值: {memory_info['percent']}%，执行紧急清理")
                    self.emergency_cleanup()

            except Exception as e:
                log.error(f"内存监控异常: {str(e)}")

            # 等待下一次监控
            safe_sleep(interval)

    def get_memory_info(self) -> Dict[str, Any]:
        """
        获取当前内存使用情况

        Returns:
            Dict: 内存使用信息
        """
        try:
            # 获取系统内存信息
            memory = psutil.virtual_memory()

            # 获取当前进程内存信息
            process = psutil.Process(os.getpid())
            process_memory = process.memory_info()

            return {
                'total': memory.total,
                'available': memory.available,
                'used': memory.used,
                'free': memory.free,
                'percent': memory.percent,
                'process_rss': process_memory.rss,  # 常驻内存
                'process_vms': process_memory.vms,  # 虚拟内存
                'process_percent': process.memory_percent()
            }
        except Exception as e:
            log.error(f"获取内存信息失败: {str(e)}")
            return {
                'total': 0,
                'available': 0,
                'used': 0,
                'free': 0,
                'percent': 0,
                'process_rss': 0,
                'process_vms': 0,
                'process_percent': 0
            }

    def cleanup_memory(self):
        """清理内存"""
        log.info("执行内存清理...")

        # 清理缓存
        self._cleanup_cache()

        # 清理对象池
        self._cleanup_object_pools()

        # 强制垃圾回收
        collected = gc.collect()

        log.info(f"内存清理完成，回收了 {collected} 个对象")

    def emergency_cleanup(self):
        """紧急清理内存"""
        log.warning("执行紧急内存清理...")

        # 清空缓存
        self.cached_objects.clear()
        self.current_cache_size = 0

        # 清空对象池
        for pool_name in list(self.object_pools.keys()):
            self.clear_object_pool(pool_name)

        # 强制多次垃圾回收
        for _ in range(3):
            gc.collect()

        log.warning("紧急内存清理完成")

    def _cleanup_cache(self):
        """清理缓存"""
        if not self.cached_objects:
            return

        # 按最后访问时间排序
        sorted_cache = sorted(
            self.cached_objects.items(),
            key=lambda x: x[1]['last_access_time']
        )

        # 移除最旧的缓存，直到缓存大小低于限制的80%
        target_size = self.cache_size_limit * 0.8
        removed_count = 0

        while self.current_cache_size > target_size and sorted_cache:
            key, cache_info = sorted_cache.pop(0)
            if key in self.cached_objects:
                self.current_cache_size -= cache_info['size']
                del self.cached_objects[key]
                removed_count += 1

        log.info(f"缓存清理完成，移除了 {removed_count} 个对象，当前缓存大小: {self.current_cache_size / (1024*1024):.2f} MB")

    def _cleanup_object_pools(self):
        """清理对象池"""
        for pool_name, pool_info in self.object_pools.items():
            # 如果对象池大小超过限制，减少到限制的80%
            if len(pool_info['objects']) > pool_info['max_size']:
                target_size = int(pool_info['max_size'] * 0.8)
                removed_count = len(pool_info['objects']) - target_size
                pool_info['objects'] = pool_info['objects'][:target_size]
                log.info(f"对象池 {pool_name} 清理完成，移除了 {removed_count} 个对象")

    def cache_object(self, key: str, obj: Any, size: int = None) -> bool:
        """
        缓存对象

        Args:
            key: 缓存键
            obj: 要缓存的对象
            size: 对象大小（字节），如果为None则自动估算

        Returns:
            bool: 是否成功缓存
        """
        try:
            # 如果对象已存在，更新访问时间
            if key in self.cached_objects:
                self.cached_objects[key]['last_access_time'] = get_timestamp()
                return True

            # 估算对象大小
            if size is None:
                size = sys.getsizeof(obj)
                # 对于复杂对象，尝试更准确的估算
                if hasattr(obj, '__sizeof__'):
                    try:
                        size = obj.__sizeof__()
                    except:
                        pass

            # 检查是否超过缓存大小限制
            if self.current_cache_size + size > self.cache_size_limit:
                # 尝试清理缓存
                self._cleanup_cache()

                # 如果仍然超过限制，拒绝缓存
                if self.current_cache_size + size > self.cache_size_limit:
                    log.warning(f"缓存对象 {key} 失败，大小 {size} 字节超过限制")
                    return False

            # 缓存对象
            self.cached_objects[key] = {
                'object': obj,
                'size': size,
                'creation_time': get_timestamp(),
                'last_access_time': get_timestamp()
            }

            self.current_cache_size += size
            return True

        except Exception as e:
            log.error(f"缓存对象失败: {str(e)}")
            return False

    def get_cached_object(self, key: str) -> Optional[Any]:
        """
        获取缓存对象

        Args:
            key: 缓存键

        Returns:
            Any: 缓存对象，如果不存在则返回None
        """
        if key in self.cached_objects:
            # 更新访问时间
            self.cached_objects[key]['last_access_time'] = get_timestamp()
            return self.cached_objects[key]['object']
        return None

    def remove_cached_object(self, key: str) -> bool:
        """
        移除缓存对象

        Args:
            key: 缓存键

        Returns:
            bool: 是否成功移除
        """
        if key in self.cached_objects:
            self.current_cache_size -= self.cached_objects[key]['size']
            del self.cached_objects[key]
            return True
        return False

    def create_object_pool(self, pool_name: str, factory_func: Callable, max_size: int = 100) -> bool:
        """
        创建对象池

        Args:
            pool_name: 对象池名称
            factory_func: 对象工厂函数
            max_size: 对象池最大大小

        Returns:
            bool: 是否成功创建
        """
        if pool_name in self.object_pools:
            log.warning(f"对象池 {pool_name} 已存在")
            return False

        self.object_pools[pool_name] = {
            'factory': factory_func,
            'objects': [],
            'max_size': max_size
        }

        log.info(f"对象池 {pool_name} 创建成功，最大大小: {max_size}")
        return True

    def get_object_from_pool(self, pool_name: str) -> Optional[Any]:
        """
        从对象池获取对象

        Args:
            pool_name: 对象池名称

        Returns:
            Any: 对象，如果对象池不存在则返回None
        """
        if pool_name not in self.object_pools:
            log.warning(f"对象池 {pool_name} 不存在")
            return None

        pool_info = self.object_pools[pool_name]

        # 如果对象池为空，创建新对象
        if not pool_info['objects']:
            try:
                return pool_info['factory']()
            except Exception as e:
                log.error(f"创建对象失败: {str(e)}")
                return None

        # 从对象池中取出对象
        return pool_info['objects'].pop()

    def return_object_to_pool(self, pool_name: str, obj: Any) -> bool:
        """
        将对象返回到对象池

        Args:
            pool_name: 对象池名称
            obj: 要返回的对象

        Returns:
            bool: 是否成功返回
        """
        if pool_name not in self.object_pools:
            log.warning(f"对象池 {pool_name} 不存在")
            return False

        pool_info = self.object_pools[pool_name]

        # 如果对象池已满，丢弃对象
        if len(pool_info['objects']) >= pool_info['max_size']:
            return False

        # 将对象添加到对象池
        pool_info['objects'].append(obj)
        return True

    def clear_object_pool(self, pool_name: str) -> bool:
        """
        清空对象池

        Args:
            pool_name: 对象池名称

        Returns:
            bool: 是否成功清空
        """
        if pool_name not in self.object_pools:
            log.warning(f"对象池 {pool_name} 不存在")
            return False

        self.object_pools[pool_name]['objects'] = []
        return True

    def get_memory_usage_history(self) -> List[Dict[str, Any]]:
        """
        获取内存使用历史

        Returns:
            List[Dict]: 内存使用历史记录
        """
        return self.memory_usage_history

    def get_cache_info(self) -> Dict[str, Any]:
        """
        获取缓存信息

        Returns:
            Dict: 缓存信息
        """
        return {
            'size': self.current_cache_size,
            'limit': self.cache_size_limit,
            'usage_percent': (self.current_cache_size / self.cache_size_limit * 100) if self.cache_size_limit > 0 else 0,
            'object_count': len(self.cached_objects)
        }

    def get_object_pool_info(self) -> Dict[str, Any]:
        """
        获取对象池信息

        Returns:
            Dict: 对象池信息
        """
        pool_info = {}
        for pool_name, info in self.object_pools.items():
            pool_info[pool_name] = {
                'size': len(info['objects']),
                'max_size': info['max_size'],
                'usage_percent': (len(info['objects']) / info['max_size'] * 100) if info['max_size'] > 0 else 0
            }
        return pool_info

    def log_memory_usage(self, context: str = ""):
        """
        记录当前内存使用情况

        Args:
            context: 上下文信息
        """
        try:
            memory_info = self.get_memory_info()

            # 格式化内存信息
            total_gb = memory_info['total'] / (1024**3)
            used_gb = memory_info['used'] / (1024**3)
            available_gb = memory_info['available'] / (1024**3)
            process_mb = memory_info['process_rss'] / (1024**2)

            # 构建日志消息
            log_msg = f"内存使用情况"
            if context:
                log_msg += f" ({context})"
            log_msg += f": 系统 {used_gb:.1f}/{total_gb:.1f}GB ({memory_info['percent']:.1f}%), "
            log_msg += f"进程 {process_mb:.1f}MB ({memory_info['process_percent']:.1f}%)"

            log.info(log_msg)

            # 检查是否需要警告
            if memory_info['percent'] > self.memory_thresholds['warning']:
                log.warning(f"系统内存使用率较高: {memory_info['percent']:.1f}%")

            if memory_info['process_percent'] > 10.0:  # 进程使用超过10%系统内存
                log.warning(f"进程内存使用率较高: {memory_info['process_percent']:.1f}%")

        except Exception as e:
            log.error(f"记录内存使用情况失败: {str(e)}")

    def format_memory_size(self, size_bytes: int) -> str:
        """
        格式化内存大小显示

        Args:
            size_bytes: 字节数

        Returns:
            str: 格式化的大小字符串
        """
        if size_bytes < 1024:
            return f"{size_bytes}B"
        elif size_bytes < 1024**2:
            return f"{size_bytes/1024:.1f}KB"
        elif size_bytes < 1024**3:
            return f"{size_bytes/(1024**2):.1f}MB"
        else:
            return f"{size_bytes/(1024**3):.1f}GB"
