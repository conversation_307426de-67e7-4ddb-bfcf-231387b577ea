#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法最小统一配置模块
第一步：确保测试模式和生产模式使用相同的基础参数
"""

import logging

log = logging.getLogger(__name__)

class RectPackConfig:
    """RectPack算法统一配置 - 最小实现"""
    
    def __init__(self):
        # 核心参数 - 确保测试和生产模式一致
        self.rotation_enabled = True
        self.sort_by_area = True  # 按面积排序
        self.spacing_px = 1
        
        log.info("RectPack统一配置初始化完成")
    
    def get_packer_params(self):
        """获取装箱器参数"""
        return {
            'rotation': self.rotation_enabled
        }
    
    def get_sort_function(self):
        """获取排序函数 - 与测试模式保持一致"""
        def sort_by_area(rect_list):
            return sorted(rect_list, key=lambda rect: rect[0] * rect[1], reverse=True)
        return sort_by_area

# 全局配置实例
config = RectPackConfig()
