#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack生产模式修复验证脚本
验证两个核心问题的修复效果：
1. 生产模式是否使用了与测试模式一致的参数
2. 生产模式是否生成TXT说明文档
"""

import logging
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

def test_unified_params_consistency():
    """测试统一参数的一致性"""
    log.info("=== 测试统一参数一致性 ===")
    
    try:
        # 测试统一参数模块
        from core.rectpack_params import unified_params
        
        # 获取参数
        rectpack_params = unified_params.get_rectpack_params()
        sort_function = unified_params.get_sort_function()
        
        log.info(f"统一参数 - 旋转启用: {unified_params.rotation_enabled}")
        log.info(f"统一参数 - 图片间距: {unified_params.spacing_px}px")
        log.info(f"统一参数 - RectPack参数: {rectpack_params}")
        
        # 测试排序函数
        test_rects = [(120, 60), (80, 50), (100, 80)]
        sorted_rects = sort_function(test_rects)
        log.info(f"排序测试 - 原始: {test_rects}")
        log.info(f"排序测试 - 排序后: {sorted_rects}")
        
        # 验证排序是否按面积降序
        areas = [rect[0] * rect[1] for rect in sorted_rects]
        is_descending = all(areas[i] >= areas[i+1] for i in range(len(areas)-1))
        
        if is_descending:
            log.info("✅ 排序函数正确：按面积降序排列")
            return True
        else:
            log.error("❌ 排序函数错误：未按面积降序排列")
            return False
            
    except Exception as e:
        log.error(f"❌ 统一参数测试失败: {str(e)}")
        return False

def test_rectpack_arranger_params():
    """测试RectPack排列器是否使用统一参数"""
    log.info("=== 测试RectPack排列器参数 ===")
    
    try:
        from core.rectpack_arranger import RectPackArranger
        from core.rectpack_params import unified_params
        
        # 创建排列器
        arranger = RectPackArranger(
            container_width=200,
            image_spacing=1,
            max_height=300,
            log_signal=None
        )
        
        # 检查排列器是否使用了统一参数
        expected_rotation = unified_params.rotation_enabled
        actual_rotation = arranger.rotation_enabled
        
        log.info(f"期望旋转设置: {expected_rotation}")
        log.info(f"实际旋转设置: {actual_rotation}")
        
        if expected_rotation == actual_rotation:
            log.info("✅ RectPack排列器使用了统一的旋转参数")
            
            # 测试排序函数
            sort_func = arranger._create_optimized_sort_function()
            test_rects = [(120, 60), (80, 50), (100, 80)]
            sorted_rects = sort_func(test_rects)
            
            # 验证排序
            areas = [rect[0] * rect[1] for rect in sorted_rects]
            is_descending = all(areas[i] >= areas[i+1] for i in range(len(areas)-1))
            
            if is_descending:
                log.info("✅ RectPack排列器使用了统一的排序函数")
                return True
            else:
                log.error("❌ RectPack排列器排序函数不一致")
                return False
        else:
            log.error("❌ RectPack排列器未使用统一的旋转参数")
            return False
            
    except Exception as e:
        log.error(f"❌ RectPack排列器参数测试失败: {str(e)}")
        return False

def test_txt_generator():
    """测试TXT文档生成器"""
    log.info("=== 测试TXT文档生成器 ===")
    
    try:
        from core.rectpack_txt_generator import txt_generator
        
        # 模拟图片数据
        test_images = [
            {
                'name': 'TestImage_1',
                'width': 120,
                'height': 60,
                'x': 0,
                'y': 0,
                'rotated': False,
                'need_rotation': False
            },
            {
                'name': 'TestImage_2',
                'width': 80,
                'height': 50,
                'x': 120,
                'y': 0,
                'rotated': True,
                'need_rotation': True
            }
        ]
        
        # 生成测试文档
        test_doc_path = "test_rectpack_production_doc.txt"
        success = txt_generator.generate_production_txt_doc(
            doc_path=test_doc_path,
            arranged_images=test_images,
            canvas_width_px=200,
            canvas_height_px=100,
            material_name="测试材质",
            canvas_sequence=1,
            ppi=72
        )
        
        if success and os.path.exists(test_doc_path):
            log.info("✅ TXT文档生成成功")
            
            # 检查文档内容
            with open(test_doc_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 验证关键内容
            required_sections = [
                "RectPack算法生产模式报告",
                "★ 基本信息",
                "★ 容器详情", 
                "★ 布局统计",
                "★ 详细图片信息",
                "★ 技术说明",
                "★ 算法参数"
            ]
            
            missing_sections = []
            for section in required_sections:
                if section not in content:
                    missing_sections.append(section)
            
            if not missing_sections:
                log.info("✅ TXT文档包含所有必需的章节")
                
                # 检查图片信息表格
                if "TestImage_1" in content and "TestImage_2" in content:
                    log.info("✅ TXT文档包含图片详细信息")
                    
                    # 清理测试文件
                    os.remove(test_doc_path)
                    return True
                else:
                    log.error("❌ TXT文档缺少图片详细信息")
                    return False
            else:
                log.error(f"❌ TXT文档缺少章节: {missing_sections}")
                return False
        else:
            log.error("❌ TXT文档生成失败")
            return False
            
    except Exception as e:
        log.error(f"❌ TXT文档生成器测试失败: {str(e)}")
        return False

def test_production_mode_integration():
    """测试生产模式集成"""
    log.info("=== 测试生产模式集成 ===")
    
    try:
        # 检查生产模式是否正确导入统一模块
        from ui.rectpack_layout_worker import RectPackLayoutWorker
        
        log.info("✅ 生产模式工作器导入成功")
        
        # 检查是否能正确导入TXT生成器
        try:
            from core.rectpack_txt_generator import txt_generator
            log.info("✅ 生产模式可以访问TXT生成器")
        except ImportError as e:
            log.error(f"❌ 生产模式无法访问TXT生成器: {str(e)}")
            return False
        
        # 检查是否能正确导入统一参数
        try:
            from core.rectpack_params import unified_params
            log.info("✅ 生产模式可以访问统一参数")
        except ImportError as e:
            log.error(f"❌ 生产模式无法访问统一参数: {str(e)}")
            return False
        
        return True
        
    except Exception as e:
        log.error(f"❌ 生产模式集成测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    log.info("开始RectPack生产模式修复验证")
    
    tests = [
        ("统一参数一致性", test_unified_params_consistency),
        ("RectPack排列器参数", test_rectpack_arranger_params),
        ("TXT文档生成器", test_txt_generator),
        ("生产模式集成", test_production_mode_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        log.info(f"\n--- 测试 {test_name} ---")
        if test_func():
            passed += 1
            log.info(f"✅ {test_name} 测试通过")
        else:
            log.error(f"❌ {test_name} 测试失败")
    
    log.info(f"\n=== 测试结果 ===")
    log.info(f"通过: {passed}/{total}")
    log.info(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        log.info("🎉 所有RectPack生产模式修复验证通过！")
        log.info("核心问题修复状态:")
        log.info("  ✅ 问题1: 生产模式现在使用与测试模式一致的参数")
        log.info("  ✅ 问题2: 生产模式现在生成TXT说明文档")
        return True
    else:
        log.error("❌ 部分RectPack生产模式修复验证失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
