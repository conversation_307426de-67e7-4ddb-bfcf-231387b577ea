#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法参数统一模块
第二步：统一测试模式和生产模式的基础参数
"""

import logging

log = logging.getLogger(__name__)

class RectPackParams:
    """RectPack算法统一参数类"""
    
    def __init__(self):
        # 核心参数 - 确保测试和生产模式完全一致
        self.rotation_enabled = True
        self.spacing_px = 1
        
        # 排序策略 - 使用最简单的面积排序
        self.sort_by_area = True
        
        log.info("RectPack统一参数初始化完成")
    
    def get_rectpack_params(self):
        """获取RectPack库的参数"""
        return {
            'rotation': self.rotation_enabled
        }
    
    def get_sort_function(self):
        """获取统一的排序函数"""
        def sort_by_area_desc(rect_list):
            """按面积降序排列 - 测试和生产模式统一使用"""
            return sorted(rect_list, key=lambda rect: rect[0] * rect[1], reverse=True)
        
        return sort_by_area_desc
    
    def log_params(self):
        """记录当前参数"""
        log.info("当前RectPack统一参数:")
        log.info(f"  旋转启用: {self.rotation_enabled}")
        log.info(f"  图片间距: {self.spacing_px}px")
        log.info(f"  排序策略: 按面积降序")

# 全局参数实例
unified_params = RectPackParams()
