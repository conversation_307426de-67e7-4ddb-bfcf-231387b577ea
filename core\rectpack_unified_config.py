#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法统一配置模块
确保测试模式和生产模式使用完全一致的算法参数和处理逻辑

模块化设计原则：
1. DRY原则：统一配置，避免重复
2. KISS原则：简单直接的配置管理
3. SOLID原则：单一职责，配置与算法分离
4. YAGNI原则：只实现必要的配置项
"""

import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass

log = logging.getLogger(__name__)

@dataclass
class RectPackConfig:
    """RectPack算法统一配置类"""
    
    # 排序策略
    sort_strategy: str = 'AREA'  # 按面积排序，与测试模式保持一致
    
    # 旋转设置
    rotation_enabled: bool = True
    
    # 装箱算法
    pack_algorithm: str = 'BSSF'  # Best Short Side Fit
    
    # 间距设置
    spacing_px: int = 1
    
    # 容器设置
    bin_selection_strategy: int = 0  # 简化为单一策略
    
    # 调试设置
    debug_mode: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'sort_strategy': self.sort_strategy,
            'rotation_enabled': self.rotation_enabled,
            'pack_algorithm': self.pack_algorithm,
            'spacing_px': self.spacing_px,
            'bin_selection_strategy': self.bin_selection_strategy,
            'debug_mode': self.debug_mode
        }


class RectPackUnifiedConfig:
    """RectPack算法统一配置管理器"""
    
    _instance = None
    _config = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._config is None:
            self._config = RectPackConfig()
            log.info("RectPack统一配置初始化完成")
    
    def get_config(self) -> RectPackConfig:
        """获取配置"""
        return self._config
    
    def update_config(self, **kwargs) -> None:
        """更新配置"""
        for key, value in kwargs.items():
            if hasattr(self._config, key):
                setattr(self._config, key, value)
                log.info(f"更新RectPack配置: {key} = {value}")
            else:
                log.warning(f"未知的配置项: {key}")
    
    def get_rectpack_packer_params(self) -> Dict[str, Any]:
        """获取RectPack装箱器参数"""
        return {
            'rotation': self._config.rotation_enabled
        }
    
    def get_sort_function(self):
        """获取排序函数"""
        def area_sort(rect_list):
            """按面积排序 - 与测试模式保持一致"""
            return sorted(rect_list, key=lambda rect: rect[0] * rect[1], reverse=True)
        
        return area_sort
    
    def log_current_config(self) -> None:
        """记录当前配置"""
        config_dict = self._config.to_dict()
        log.info("当前RectPack统一配置:")
        for key, value in config_dict.items():
            log.info(f"  {key}: {value}")


# 全局配置实例
unified_config = RectPackUnifiedConfig()

def get_unified_config() -> RectPackUnifiedConfig:
    """获取全局统一配置实例"""
    return unified_config
