#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法修复测试脚本
测试模块化修复的效果
"""

import logging
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

def test_unified_params():
    """测试统一参数模块"""
    log.info("=== 测试统一参数模块 ===")
    
    try:
        from core.rectpack_params import unified_params
        
        # 测试参数获取
        rectpack_params = unified_params.get_rectpack_params()
        sort_function = unified_params.get_sort_function()
        
        log.info(f"RectPack参数: {rectpack_params}")
        log.info("排序函数测试:")
        
        # 测试排序函数
        test_rects = [(100, 50), (80, 60), (120, 40)]
        sorted_rects = sort_function(test_rects)
        log.info(f"  原始: {test_rects}")
        log.info(f"  排序后: {sorted_rects}")
        
        unified_params.log_params()
        log.info("✅ 统一参数模块测试通过")
        return True
        
    except Exception as e:
        log.error(f"❌ 统一参数模块测试失败: {str(e)}")
        return False

def test_txt_generator():
    """测试TXT文档生成模块"""
    log.info("=== 测试TXT文档生成模块 ===")
    
    try:
        from core.rectpack_txt_generator import txt_generator
        
        # 模拟图片数据
        test_images = [
            {'name': 'Image_1', 'width': 100, 'height': 50, 'x': 0, 'y': 0, 'rotated': False},
            {'name': 'Image_2', 'width': 80, 'height': 60, 'x': 100, 'y': 0, 'rotated': True},
        ]
        
        # 生成测试文档
        test_doc_path = "test_rectpack_doc.txt"
        success = txt_generator.generate_production_txt_doc(
            doc_path=test_doc_path,
            arranged_images=test_images,
            canvas_width_px=200,
            canvas_height_px=100,
            material_name="测试材质",
            canvas_sequence=1,
            ppi=72
        )
        
        if success and os.path.exists(test_doc_path):
            log.info("✅ TXT文档生成模块测试通过")
            # 清理测试文件
            os.remove(test_doc_path)
            return True
        else:
            log.error("❌ TXT文档生成失败")
            return False
            
    except Exception as e:
        log.error(f"❌ TXT文档生成模块测试失败: {str(e)}")
        return False

def test_diagnostic():
    """测试诊断模块"""
    log.info("=== 测试诊断模块 ===")
    
    try:
        from core.rectpack_diagnostic import log_diagnostic_results
        
        differences = log_diagnostic_results()
        
        if differences and len(differences) > 0:
            log.info("✅ 诊断模块测试通过")
            return True
        else:
            log.error("❌ 诊断模块返回空结果")
            return False
            
    except Exception as e:
        log.error(f"❌ 诊断模块测试失败: {str(e)}")
        return False

def test_rotation_fix():
    """测试旋转修复模块"""
    log.info("=== 测试旋转修复模块 ===")
    
    try:
        from core.rectpack_rotation_fix import fix_photoshop_rotation_script, log_rotation_fix_info
        
        # 测试旋转脚本生成
        script_90 = fix_photoshop_rotation_script(90)
        script_180 = fix_photoshop_rotation_script(180)
        
        if "rotateCanvas(90)" in script_90 and "rotateCanvas(180)" in script_180:
            log.info("✅ 旋转脚本生成正确")
            log_rotation_fix_info()
            return True
        else:
            log.error("❌ 旋转脚本生成错误")
            return False
            
    except Exception as e:
        log.error(f"❌ 旋转修复模块测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    log.info("开始RectPack算法修复测试")
    
    tests = [
        ("统一参数模块", test_unified_params),
        ("TXT文档生成模块", test_txt_generator),
        ("诊断模块", test_diagnostic),
        ("旋转修复模块", test_rotation_fix),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        log.info(f"\n--- 测试 {test_name} ---")
        if test_func():
            passed += 1
        else:
            log.error(f"{test_name} 测试失败")
    
    log.info(f"\n=== 测试结果 ===")
    log.info(f"通过: {passed}/{total}")
    log.info(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        log.info("🎉 所有模块化修复测试通过！")
        return True
    else:
        log.error("❌ 部分模块化修复测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
