#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法测试模式核心模块
完全按照 tests/test_rectpack_real_data.py 的方式实现

核心特性：
1. 完全按照test_rectpack_real_data.py的方式，图片和画布忽略cm单位替换为px
2. 考虑多容器问题（当一组数据图片达到容器最大高度时新开一块容器）
3. 考虑图片太少时，容器高度以最后一个图片的尾部为高度，重设容器高度，再计算利用率
4. 用matplotlib的方式绘制可视化结果，并按图片说明格式写每个可视化结果的说明文档
5. 分步骤、分阶段、模块化实现，遵循DRY、KISS、SOLID、YAGNI原则
"""

import logging
import time
import os
from typing import List, Tuple, Dict, Any, Optional
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 配置日志
log = logging.getLogger(__name__)

# ============================================================================
# 第一阶段：数据准备模块 - 按照test_rectpack_real_data.py标准
# ============================================================================

def convert_pattern_items_to_px_data(pattern_items: List[Dict[str, Any]]) -> List[Tuple[int, int, str]]:
    """
    将图案项目转换为px数据格式，统一单位处理：cm直接改用px进行算法
    优化版本：确保测试模式和生产模式使用相同的数据处理逻辑

    Args:
        pattern_items: 图案项目列表，包含width_cm, height_cm等字段

    Returns:
        List[Tuple[int, int, str]]: (宽度px, 高度px, 标识) 的元组列表
    """
    from utils.unit_converter import cm_to_px_test_mode

    px_data = []
    log.info(f"开始转换 {len(pattern_items)} 个图案项目为px数据（测试模式统一处理）")

    for i, item in enumerate(pattern_items):
        try:
            # 提取cm尺寸
            width_cm = item.get('width_cm', 0)
            height_cm = item.get('height_cm', 0)

            # 验证尺寸有效性
            if width_cm <= 0 or height_cm <= 0:
                log.warning(f"跳过无效尺寸的图案 {i+1}: {width_cm}x{height_cm}cm")
                continue

            # 使用统一的测试模式转换函数：cm直接改用px，实现1:1转换
            # 例如：图片尺寸 120x60 cm，在测试模式下认为是 120x60 px
            width_px = cm_to_px_test_mode(width_cm)
            height_px = cm_to_px_test_mode(height_cm)

            # 创建唯一标识，确保与生产模式一致
            pattern_name = item.get('pattern_name', f'Image_{i+1}')
            # 添加更多信息以确保唯一性
            unique_suffix = f"{width_px}x{height_px}_{i}"
            label = f"{pattern_name}_{unique_suffix}"

            # 处理数量，如果有数量字段则重复添加
            quantity = max(1, item.get('quantity', 1))  # 确保至少为1
            for q in range(quantity):
                if quantity > 1:
                    unique_label = f"{label}_copy{q+1}"
                else:
                    unique_label = label

                px_data.append((width_px, height_px, unique_label))

        except Exception as e:
            log.error(f"转换第{i+1}个图案项目失败: {str(e)}")
            continue

    log.info(f"图案项目转换完成: {len(pattern_items)} -> {len(px_data)} 张图片 (cm直接转px，1:1比例)")
    return px_data


def get_container_config_px(canvas_width_cm: float,
                           horizontal_expansion_cm: float = 0,
                           max_height_cm: float = 5000,
                           image_spacing_cm: float = 0.1) -> Dict[str, int]:
    """
    获取容器配置，统一单位处理：cm直接改用px进行算法
    优化版本：确保测试模式和生产模式使用相同的容器配置逻辑

    Args:
        canvas_width_cm: 画布宽度(cm)
        horizontal_expansion_cm: 水平拓展(cm)
        max_height_cm: 最大高度(cm)，测试模式下严格遵循此限制
        image_spacing_cm: 图片间距(cm)

    Returns:
        Dict[str, int]: 容器配置（px单位）
    """
    from utils.unit_converter import get_container_config_unified

    log.info(f"获取容器配置（测试模式）: 画布{canvas_width_cm}cm + 拓展{horizontal_expansion_cm}cm, 最大高度{max_height_cm}cm, 间距{image_spacing_cm}cm")

    # 使用统一的容器配置函数，指定为测试模式
    unified_config = get_container_config_unified(
        canvas_width_cm=canvas_width_cm,
        horizontal_expansion_cm=horizontal_expansion_cm,
        max_height_cm=max_height_cm,
        image_spacing_cm=image_spacing_cm,
        is_test_mode=True  # 测试模式：cm直接转px
    )

    # 转换为旧的接口格式（保持兼容性）
    config = {
        'base_width': unified_config['base_width'],
        'horizontal_expansion': unified_config['horizontal_expansion'],
        'actual_width': unified_config['actual_width'],
        'max_height': unified_config['max_height'],
        'spacing': unified_config['spacing'],
        'unit_conversion': unified_config['conversion_method']
    }

    log.info(f"容器配置完成: 实际宽度{config['actual_width']}px, 最大高度{config['max_height']}px, 间距{config['spacing']}px")
    return config


def validate_px_test_data(px_data: List[Tuple[int, int, str]]) -> bool:
    """
    验证px测试数据的有效性，按照test_rectpack_real_data.py标准

    Args:
        px_data: px数据列表

    Returns:
        bool: 数据是否有效
    """
    if not px_data:
        log.error("测试数据为空")
        return False

    for i, (width, height, label) in enumerate(px_data):
        if width <= 0 or height <= 0:
            log.error(f"第{i+1}张图片尺寸无效: {width}x{height}")
            return False

        if not label:
            log.error(f"第{i+1}张图片标识为空")
            return False

    log.info(f"数据验证通过：{len(px_data)} 张图片")
    return True


def convert_to_image_objects(px_data: List[Tuple[int, int, str]]) -> List[Dict[str, Any]]:
    """
    将px数据转换为图片对象，按照test_rectpack_real_data.py标准

    Args:
        px_data: px数据列表

    Returns:
        List[Dict[str, Any]]: 图片对象列表
    """
    images = []

    for i, (width, height, label) in enumerate(px_data):
        image_obj = {
            'id': i + 1,
            'width': width,
            'height': height,
            'label': label,
            'area': width * height,
            'aspect_ratio': width / height if height > 0 else 0
        }
        images.append(image_obj)

    log.info(f"转换完成：{len(images)} 个图片对象")
    return images


# ============================================================================
# 第二阶段：RectPack算法核心模块 - 支持多容器
# ============================================================================

def create_rectpack_config() -> Dict[str, Any]:
    """
    创建RectPack算法配置，按照test_rectpack_real_data.py标准

    Returns:
        Dict[str, Any]: RectPack配置
    """
    try:
        from rectpack import newPacker, SORT_AREA, SORT_PERI, SORT_DIFF, SORT_SSIDE, SORT_LSIDE, SORT_RATIO
        from rectpack import PackerBNF, PackerBFF, PackerBBF

        config = {
            'available': True,
            'sort_strategies': {
                'AREA': SORT_AREA,
                'PERIMETER': SORT_PERI,
                'DIFFERENCE': SORT_DIFF,
                'SHORT_SIDE': SORT_SSIDE,
                'LONG_SIDE': SORT_LSIDE,
                'RATIO': SORT_RATIO
            },
            'pack_algorithms': {
                'BNF': PackerBNF,  # Bottom-Left Fill
                'BFF': PackerBFF,  # Best Fit First
                'BBF': PackerBBF   # Best Bin First
            }
        }

        log.info("RectPack库可用，配置创建成功")
        return config

    except ImportError as e:
        log.warning(f"RectPack库不可用：{str(e)}，将使用简化算法")
        return {'available': False}


def create_rectpack_packer(container_config: Dict[str, int],
                          sort_strategy: str = 'AREA',
                          rotation_enabled: bool = True) -> Any:
    """
    创建RectPack装箱器，按照test_rectpack_real_data.py标准

    Args:
        container_config: 容器配置
        sort_strategy: 排序策略
        rotation_enabled: 是否启用旋转

    Returns:
        装箱器实例或None
    """
    rectpack_config = create_rectpack_config()

    if not rectpack_config['available']:
        log.error("RectPack库不可用")
        return None

    try:
        from rectpack import newPacker

        # 获取排序策略
        sort_key = rectpack_config['sort_strategies'].get(sort_strategy,
                                                         rectpack_config['sort_strategies']['AREA'])

        # 创建装箱器
        packer = newPacker(
            rotation=rotation_enabled
        )

        # 添加容器 - 使用实际宽度（包含水平拓展）
        packer.add_bin(container_config['actual_width'], container_config['max_height'])

        log.info(f"创建RectPack装箱器：{container_config['actual_width']}x{container_config['max_height']}px，"
                f"排序策略={sort_strategy}，旋转={'启用' if rotation_enabled else '禁用'}")

        return packer

    except Exception as e:
        log.error(f"创建RectPack装箱器失败：{str(e)}")
        return None


def place_images_with_rectpack_multi_container(images: List[Dict[str, Any]],
                                             container_config: Dict[str, int],
                                             spacing: int = 1) -> List[List[Dict[str, Any]]]:
    """
    使用RectPack算法放置图片，支持多容器，按照test_rectpack_real_data.py标准

    Args:
        images: 图片列表
        container_config: 容器配置
        spacing: 图片间距

    Returns:
        List[List[Dict[str, Any]]]: 多个容器的已放置图片列表
    """
    if not images:
        log.error("图片列表为空，无法放置图片")
        return []

    all_containers = []
    remaining_images = images.copy()
    container_index = 1

    while remaining_images:
        log.info(f"开始处理第{container_index}个容器，剩余图片: {len(remaining_images)}张")

        # 为当前容器创建装箱器
        packer = create_rectpack_packer(container_config, 'AREA', True)
        if packer is None:
            log.error("无法创建装箱器，停止处理")
            break

        # 尝试放置剩余的图片
        placed_images, unplaced_images = place_images_single_container(
            packer, remaining_images, container_config, spacing
        )

        if placed_images:
            # 重设容器高度（当图片太少时）
            adjusted_container = adjust_container_height(placed_images, container_config)
            all_containers.append(adjusted_container)

            log.info(f"第{container_index}个容器完成，放置图片: {len(placed_images)}张")
        else:
            log.warning(f"第{container_index}个容器无法放置任何图片")
            break

        # 更新剩余图片
        remaining_images = unplaced_images
        container_index += 1

        # 防止无限循环
        if container_index > 100:
            log.error("容器数量超过限制，停止处理")
            break

    log.info(f"多容器处理完成，总容器数: {len(all_containers)}")
    return all_containers


def place_images_single_container(packer: Any,
                                images: List[Dict[str, Any]],
                                container_config: Dict[str, int],
                                spacing: int = 1) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    在单个容器中放置图片

    Args:
        packer: RectPack装箱器
        images: 图片列表
        container_config: 容器配置
        spacing: 图片间距

    Returns:
        Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]: (已放置的图片, 未放置的图片)
    """
    placed_images = []
    unplaced_images = []

    try:
        # 添加所有矩形到装箱器
        for img in images:
            width_with_spacing = img['width'] + spacing
            height_with_spacing = img['height'] + spacing
            packer.add_rect(width_with_spacing, height_with_spacing, rid=img['id'])

        # 执行装箱
        start_time = time.time()
        packer.pack()
        pack_time = time.time() - start_time

        log.info(f"装箱计算完成，耗时：{pack_time:.3f}秒")

        # 获取放置结果
        placed_rects = packer.rect_list()
        placed_ids = set()

        for bin_id, x, y, width, height, rect_id in placed_rects:
            # 找到对应的图片
            original_img = next((img for img in images if img['id'] == rect_id), None)
            if original_img:
                placed_img = {
                    'id': rect_id,
                    'x': x,
                    'y': y,
                    'width': width - spacing,  # 减去间距得到实际图片尺寸
                    'height': height - spacing,
                    'original_width': original_img['width'],
                    'original_height': original_img['height'],
                    'label': original_img['label'],
                    'rotated': (width - spacing != original_img['width'] or
                               height - spacing != original_img['height'])
                }
                placed_images.append(placed_img)
                placed_ids.add(rect_id)

        # 找出未放置的图片
        for img in images:
            if img['id'] not in placed_ids:
                unplaced_images.append(img)

        log.info(f"单容器放置完成: {len(placed_images)}/{len(images)} 张图片")

    except Exception as e:
        log.error(f"单容器图片放置过程出错：{str(e)}")
        unplaced_images = images.copy()

    return placed_images, unplaced_images


def adjust_container_height(placed_images: List[Dict[str, Any]],
                          container_config: Dict[str, int]) -> Dict[str, Any]:
    """
    重设容器高度，当图片太少时以最后一个图片的尾部为高度
    测试模式下严格遵循配置的最大高度限制

    Args:
        placed_images: 已放置的图片列表
        container_config: 容器配置

    Returns:
        Dict[str, Any]: 调整后的容器信息
    """
    if not placed_images:
        return {
            'placed_images': [],
            'container_config': container_config,
            'actual_height': 0,
            'utilization_rate': 0.0
        }

    # 计算实际使用的画布高度
    max_y = max(img['y'] + img['height'] for img in placed_images)

    # 添加一些边距，但不能超过最大高度限制
    spacing = container_config.get('spacing', 1)
    desired_height = max_y + spacing * 2

    # 测试模式下严格遵循最大高度限制
    max_allowed_height = container_config['max_height']
    actual_height = min(desired_height, max_allowed_height)

    # 确保最小高度
    actual_height = max(actual_height, 100)

    # 如果调整后的高度小于期望高度，记录警告
    if actual_height < desired_height:
        log.warning(f"容器高度受限：期望高度 {desired_height}px，实际使用 {actual_height}px "
                   f"（最大限制 {max_allowed_height}px）")

    # 创建调整后的容器配置
    adjusted_config = container_config.copy()
    adjusted_config['actual_height'] = actual_height

    # 计算利用率
    used_area = sum(img['width'] * img['height'] for img in placed_images)
    container_area = container_config['actual_width'] * actual_height
    utilization_rate = (used_area / container_area * 100) if container_area > 0 else 0

    log.info(f"容器高度调整: 最大限制{container_config['max_height']}px -> 实际使用{actual_height}px, "
             f"利用率: {utilization_rate:.2f}%")

    return {
        'placed_images': placed_images,
        'container_config': adjusted_config,
        'actual_height': actual_height,
        'utilization_rate': utilization_rate,
        'used_area': used_area,
        'container_area': container_area
    }


# ============================================================================
# 第三阶段：matplotlib可视化模块 - 按照test_rectpack_real_data.py标准
# ============================================================================

def create_matplotlib_visualization(container_data: Dict[str, Any],
                                  output_path: str,
                                  title: str = "RectPack算法布局结果") -> bool:
    """
    创建matplotlib可视化，优化版本：更好地模拟生产环境布局效果
    使用不同颜色代表不同图片，确保相邻图片颜色不同

    Args:
        container_data: 容器数据（包含placed_images等）
        output_path: 输出文件路径
        title: 图形标题

    Returns:
        bool: 是否创建成功
    """
    if not container_data or not container_data.get('placed_images'):
        log.error("没有已放置的图片，无法创建可视化")
        return False

    try:
        placed_images = container_data['placed_images']
        container_config = container_data['container_config']

        # 计算图形尺寸，确保与生产环境比例一致
        canvas_width = container_config['actual_width']
        canvas_height = container_data['actual_height']
        aspect_ratio = canvas_height / canvas_width

        # 优化图形尺寸计算，确保清晰显示
        fig_width = min(16, max(10, canvas_width / 50))  # 根据画布宽度动态调整
        fig_height = max(6, min(20, fig_width * aspect_ratio))

        # 创建图形，使用高DPI确保清晰度
        fig, ax = plt.subplots(figsize=(fig_width, fig_height), dpi=100)

        # 设置坐标轴，Y轴翻转以匹配PS坐标系
        ax.set_xlim(0, canvas_width)
        ax.set_ylim(canvas_height, 0)  # 翻转Y轴以匹配PS坐标系
        ax.set_aspect('equal')

        # 绘制画布边界
        canvas_rect = plt.Rectangle((0, 0), canvas_width, canvas_height,
                                   fill=False, edgecolor='black', linewidth=2)
        ax.add_patch(canvas_rect)

        # 如果有水平拓展，绘制基础宽度线以显示拓展区域
        if container_config.get('horizontal_expansion', 0) > 0:
            base_width = container_config['base_width']
            ax.axvline(x=base_width, color='red', linestyle='--', linewidth=1, alpha=0.7)
            ax.text(base_width + 5, canvas_height * 0.95,
                   f'基础宽度: {base_width}px',
                   rotation=90, va='top', ha='left',
                   color='red', fontsize=8)

        # 绘制图片，使用优化的颜色方案确保相邻图片颜色不同
        colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
            '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2',
            '#A3E4D7', '#F9E79F', '#D5A6BD', '#AED6F1', '#A9DFBF'
        ]

        # 为了确保相邻图片颜色不同，使用简单的间隔策略
        def get_image_color(index: int) -> str:
            """获取图片颜色，确保相邻图片颜色不同"""
            color_index = (index * 3) % len(colors)  # 使用间隔策略
            return colors[color_index]

        for i, img in enumerate(placed_images):
            color = get_image_color(i)

            # 绘制图片矩形，优化视觉效果
            rect = plt.Rectangle(
                (img['x'], img['y']),
                img['width'],
                img['height'],
                fill=True,
                facecolor=color,
                edgecolor='black',
                linewidth=1.0,  # 增加边框粗细以更好区分
                alpha=0.8  # 提高透明度以更好显示
            )
            ax.add_patch(rect)

            # 添加标签，优化字体大小和颜色
            center_x = img['x'] + img['width'] / 2
            center_y = img['y'] + img['height'] / 2
            font_size = max(8, min(12, min(img['width'], img['height']) / 12))  # 优化字体大小

            # 创建更详细的标签信息
            label_text = str(img['id'])
            if img.get('rotated', False):
                label_text += "↻"  # 使用旋转符号而不是"R"

            # 使用对比度更高的文字颜色
            text_color = 'white' if sum(int(color[i:i+2], 16) for i in (1, 3, 5)) < 400 else 'black'

            ax.text(center_x, center_y, label_text,
                   ha='center', va='center',
                   fontsize=font_size,
                   color=text_color,
                   weight='bold',
                   bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.7, edgecolor='none'))

        # 添加统计信息
        stats_text = [
            f"成功放置: {len(placed_images)} 张图片",
            f"画布尺寸: {canvas_width}x{canvas_height} px",
            f"基础宽度: {container_config['base_width']} px",
            f"水平拓展: {container_config.get('horizontal_expansion', 0)} px",
            f"利用率: {container_data['utilization_rate']:.2f}%",
            f"旋转图片: {sum(1 for img in placed_images if img.get('rotated', False))} 张"
        ]

        text_x = canvas_width * 0.02
        text_y = canvas_height * 0.98
        text_content = "\n".join(stats_text)

        ax.text(text_x, text_y, text_content,
               verticalalignment='top',
               horizontalalignment='left',
               bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.8),
               fontsize=10)

        # 设置标题和网格
        plt.suptitle(title, fontsize=16, fontweight='bold')
        plt.grid(True, linestyle='--', alpha=0.3)
        plt.tight_layout()

        # 保存结果
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        log.info(f"可视化结果已保存到: {output_path}")

        plt.close(fig)
        return True

    except Exception as e:
        log.error(f"创建可视化失败：{str(e)}")
        return False


def create_multi_container_visualization(all_containers: List[Dict[str, Any]],
                                       output_dir: str,
                                       base_name: str) -> List[str]:
    """
    为多个容器创建可视化

    Args:
        all_containers: 所有容器数据
        output_dir: 输出目录
        base_name: 基础文件名

    Returns:
        List[str]: 生成的文件路径列表
    """
    output_files = []

    for i, container_data in enumerate(all_containers, 1):
        output_path = os.path.join(output_dir, f"{base_name}_容器{i}.png")
        title = f"{base_name} - 容器{i} - RectPack算法布局结果"

        success = create_matplotlib_visualization(container_data, output_path, title)
        if success:
            output_files.append(output_path)

    log.info(f"多容器可视化完成，生成文件: {len(output_files)}个")
    return output_files


# ============================================================================
# 第四阶段：文档生成模块 - 按照图片说明格式
# ============================================================================

def generate_container_documentation(container_data: Dict[str, Any],
                                   output_path: str,
                                   material_name: str = "测试材质",
                                   canvas_sequence: int = 1) -> bool:
    """
    生成容器的详细说明文档，按照图片说明格式

    Args:
        container_data: 容器数据
        output_path: 输出文件路径
        material_name: 材质名称
        canvas_sequence: 画布序号

    Returns:
        bool: 是否生成成功
    """
    try:
        placed_images = container_data['placed_images']
        container_config = container_data['container_config']

        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # 生成文档内容
        content = []
        content.append("# RectPack算法测试模式报告")
        content.append("")
        content.append(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        content.append("")

        # 基本信息
        content.append("★ 基本信息")
        content.append("")
        content.append(f"- 材质名称: {material_name}")
        content.append(f"- 画布序号: {canvas_sequence}")
        content.append(f"- 算法类型: RectPack统一装箱算法")
        content.append(f"- 测试模式: 启用 (px单位)")
        content.append("")

        # 容器详情
        content.append("★ 容器详情")
        content.append("")
        content.append(f"- 基础宽度: {container_config['base_width']}px")
        content.append(f"- 水平拓展: {container_config.get('horizontal_expansion', 0)}px")
        content.append(f"- 实际宽度: {container_config['actual_width']}px")
        content.append(f"- 最大高度: {container_config['max_height']}px")
        content.append(f"- 实际高度: {container_data['actual_height']}px")
        content.append(f"- 图片间距: {container_config['spacing']}px")
        content.append(f"- 单位转换: cm直接转px (测试模式)")
        content.append("")

        # 布局统计
        content.append("★ 布局统计")
        content.append("")
        content.append(f"- 成功放置图片: {len(placed_images)}张")
        content.append(f"- 已用面积: {container_data['used_area']:,}px²")
        content.append(f"- 画布面积: {container_data['container_area']:,}px²")
        content.append(f"- 利用率: {container_data['utilization_rate']:.2f}%")
        content.append(f"- 旋转图片数: {sum(1 for img in placed_images if img.get('rotated', False))}张")
        content.append(f"- 平均图片面积: {container_data['used_area'] / len(placed_images) if placed_images else 0:.0f}px²")
        content.append("")

        # 利用率评价
        utilization = container_data['utilization_rate']
        if utilization >= 85:
            rating = "★★★★★ 优秀"
        elif utilization >= 75:
            rating = "★★★★☆ 良好"
        elif utilization >= 65:
            rating = "★★★☆☆ 中等"
        elif utilization >= 50:
            rating = "★★☆☆☆ 较差"
        else:
            rating = "★☆☆☆☆ 待优化"

        content.append(f"- 利用率评价: {rating}")
        content.append("")

        # 详细图片信息
        content.append("★ 详细图片信息")
        content.append("")
        content.append("| 序号 | 图片标识 | 尺寸(px) | 位置(x,y) | 旋转 | 面积(px²) |")
        content.append("|------|----------|----------|-----------|------|-----------|")

        for img in placed_images:
            label = img.get('label', f'Image_{img["id"]}')
            width = img['width']
            height = img['height']
            x = img['x']
            y = img['y']
            rotated = '是' if img.get('rotated', False) else '否'
            area = width * height

            content.append(f"| {img['id']} | {label} | {width}x{height} | ({x},{y}) | {rotated} | {area:,} |")

        content.append("")

        # 技术说明
        content.append("★ 技术说明")
        content.append("")
        content.append("本报告使用RectPack算法生成，具有以下特点：")
        content.append("1. **统一px单位**: 完全按照test_rectpack_real_data.py标准，cm直接替换为px")
        content.append("2. **多容器支持**: 当达到最大高度时自动创建新容器")
        content.append("3. **高度重设**: 当图片太少时，容器高度以最后图片尾部为准")
        content.append("4. **水平拓展**: 支持画布宽度拓展，实际宽度 = 基础宽度 + 水平拓展")
        content.append("5. **旋转优化**: 自动旋转图片以获得更好的空间利用率")
        content.append("6. **matplotlib可视化**: 使用matplotlib绘制专业的布局图")
        content.append("")

        # 算法参数
        content.append("★ 算法参数")
        content.append("")
        content.append("- 排序策略: 按面积排序 (SORT_AREA)")
        content.append("- 装箱算法: Bottom-Left Fill (BNF)")
        content.append("- 旋转功能: 启用")
        content.append("- 间距处理: 统一间距")
        content.append("- 优化目标: 最大化空间利用率")
        content.append("")

        # 写入文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(content))

        log.info(f"容器文档已生成: {output_path}")
        return True

    except Exception as e:
        log.error(f"生成容器文档失败: {str(e)}")
        return False


def generate_multi_container_documentation(all_containers: List[Dict[str, Any]],
                                         output_dir: str,
                                         base_name: str,
                                         material_name: str = "测试材质") -> List[str]:
    """
    为多个容器生成文档

    Args:
        all_containers: 所有容器数据
        output_dir: 输出目录
        base_name: 基础文件名
        material_name: 材质名称

    Returns:
        List[str]: 生成的文档路径列表
    """
    doc_files = []

    for i, container_data in enumerate(all_containers, 1):
        doc_path = os.path.join(output_dir, f"{base_name}_容器{i}_说明.txt")

        success = generate_container_documentation(
            container_data, doc_path, material_name, i
        )
        if success:
            doc_files.append(doc_path)

    # 生成总体统计文档
    summary_path = os.path.join(output_dir, f"{base_name}_总体统计.txt")
    success = generate_summary_documentation(all_containers, summary_path, material_name)
    if success:
        doc_files.append(summary_path)

    log.info(f"多容器文档生成完成，生成文件: {len(doc_files)}个")
    return doc_files


def generate_summary_documentation(all_containers: List[Dict[str, Any]],
                                 output_path: str,
                                 material_name: str = "测试材质") -> bool:
    """
    生成总体统计文档

    Args:
        all_containers: 所有容器数据
        output_path: 输出文件路径
        material_name: 材质名称

    Returns:
        bool: 是否生成成功
    """
    try:
        # 计算总体统计
        total_images = sum(len(c['placed_images']) for c in all_containers)
        total_used_area = sum(c['used_area'] for c in all_containers)
        total_container_area = sum(c['container_area'] for c in all_containers)
        avg_utilization = (total_used_area / total_container_area * 100) if total_container_area > 0 else 0

        # 生成文档内容
        content = []
        content.append("# RectPack算法多容器总体统计报告")
        content.append("")
        content.append(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        content.append(f"材质名称: {material_name}")
        content.append("")

        # 总体统计
        content.append("★ 总体统计")
        content.append("")
        content.append(f"- 容器总数: {len(all_containers)}个")
        content.append(f"- 图片总数: {total_images}张")
        content.append(f"- 总已用面积: {total_used_area:,}px²")
        content.append(f"- 总画布面积: {total_container_area:,}px²")
        content.append(f"- 平均利用率: {avg_utilization:.2f}%")
        content.append("")

        # 各容器详情
        content.append("★ 各容器详情")
        content.append("")
        content.append("| 容器 | 图片数 | 利用率 | 实际高度 | 旋转数 |")
        content.append("|------|--------|--------|----------|--------|")

        for i, container_data in enumerate(all_containers, 1):
            img_count = len(container_data['placed_images'])
            utilization = container_data['utilization_rate']
            height = container_data['actual_height']
            rotated_count = sum(1 for img in container_data['placed_images'] if img.get('rotated', False))

            content.append(f"| 容器{i} | {img_count} | {utilization:.2f}% | {height}px | {rotated_count} |")

        content.append("")

        # 性能分析
        content.append("★ 性能分析")
        content.append("")

        utilizations = [c['utilization_rate'] for c in all_containers]
        max_util = max(utilizations) if utilizations else 0
        min_util = min(utilizations) if utilizations else 0

        content.append(f"- 最高利用率: {max_util:.2f}%")
        content.append(f"- 最低利用率: {min_util:.2f}%")
        content.append(f"- 利用率差异: {max_util - min_util:.2f}%")
        content.append(f"- 平均每容器图片数: {total_images / len(all_containers) if all_containers else 0:.1f}张")
        content.append("")

        # 写入文件
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(content))

        log.info(f"总体统计文档已生成: {output_path}")
        return True

    except Exception as e:
        log.error(f"生成总体统计文档失败: {str(e)}")
        return False


# ============================================================================
# 第五阶段：主要集成函数 - 完整的测试模式流程
# ============================================================================

def run_rectpack_test_mode(pattern_items: List[Dict[str, Any]],
                          canvas_width_cm: float,
                          horizontal_expansion_cm: float = 0,
                          max_height_cm: float = 5000,
                          image_spacing_cm: float = 0.1,
                          output_dir: str = "test_output",
                          material_name: str = "测试材质") -> Dict[str, Any]:
    """
    运行完整的RectPack测试模式，统一单位处理：cm直接改用px进行算法

    Args:
        pattern_items: 图案项目列表
        canvas_width_cm: 画布宽度(cm)
        horizontal_expansion_cm: 水平拓展(cm)
        max_height_cm: 最大高度(cm)
        image_spacing_cm: 图片间距(cm)
        output_dir: 输出目录
        material_name: 材质名称

    Returns:
        Dict[str, Any]: 测试结果
    """
    log.info("=" * 60)
    log.info("开始RectPack算法测试模式 - 统一单位处理")
    log.info("=" * 60)

    try:
        # 第一步：数据准备
        log.info("第一步：数据准备")
        px_data = convert_pattern_items_to_px_data(pattern_items)

        if not validate_px_test_data(px_data):
            return {'success': False, 'error': '数据验证失败'}

        image_objects = convert_to_image_objects(px_data)
        log.info(f"✓ 数据准备完成：{len(image_objects)} 张图片")

        # 第二步：容器配置
        log.info("第二步：容器配置")
        container_config = get_container_config_px(
            canvas_width_cm, horizontal_expansion_cm, max_height_cm, image_spacing_cm
        )
        log.info(f"✓ 容器配置完成：{container_config['actual_width']}x{container_config['max_height']}px")

        # 第三步：多容器装箱
        log.info("第三步：多容器装箱")
        start_time = time.time()
        all_containers = place_images_with_rectpack_multi_container(
            image_objects, container_config, container_config['spacing']
        )
        processing_time = time.time() - start_time

        if not all_containers:
            return {'success': False, 'error': '装箱失败，无法放置任何图片'}

        log.info(f"✓ 装箱完成：{len(all_containers)} 个容器，耗时 {processing_time:.3f}秒")

        # 第四步：可视化生成
        log.info("第四步：可视化生成")
        os.makedirs(output_dir, exist_ok=True)

        visualization_files = create_multi_container_visualization(
            all_containers, output_dir, material_name
        )
        log.info(f"✓ 可视化完成：{len(visualization_files)} 个文件")

        # 第五步：文档生成
        log.info("第五步：文档生成")
        documentation_files = generate_multi_container_documentation(
            all_containers, output_dir, material_name, material_name
        )
        log.info(f"✓ 文档生成完成：{len(documentation_files)} 个文件")

        # 计算总体统计
        total_images = sum(len(c['placed_images']) for c in all_containers)
        total_used_area = sum(c['used_area'] for c in all_containers)
        total_container_area = sum(c['container_area'] for c in all_containers)
        avg_utilization = (total_used_area / total_container_area * 100) if total_container_area > 0 else 0

        # 返回结果
        result = {
            'success': True,
            'containers': all_containers,
            'total_containers': len(all_containers),
            'total_images': total_images,
            'total_placed_images': total_images,
            'success_rate': 100.0,  # 多容器模式下所有图片都会被放置
            'avg_utilization_rate': avg_utilization,
            'processing_time': processing_time,
            'processing_speed': len(pattern_items) / processing_time if processing_time > 0 else 0,
            'visualization_files': visualization_files,
            'documentation_files': documentation_files,
            'output_dir': output_dir,
            'container_config': container_config
        }

        log.info("=" * 60)
        log.info("RectPack算法测试模式完成")
        log.info(f"✓ 容器数量: {len(all_containers)}")
        log.info(f"✓ 图片总数: {total_images}")
        log.info(f"✓ 平均利用率: {avg_utilization:.2f}%")
        log.info(f"✓ 处理速度: {result['processing_speed']:.1f} 图片/秒")
        log.info("=" * 60)

        return result

    except Exception as e:
        log.error(f"RectPack测试模式运行失败: {str(e)}")
        return {'success': False, 'error': str(e)}


def run_single_test_comparison(test_name: str,
                             test_data: List[Tuple[int, int, str]],
                             container_config: Dict[str, int],
                             output_path: str = None) -> Dict[str, Any]:
    """
    运行单个测试对比，完全按照test_rectpack_real_data.py的run_single_test标准

    Args:
        test_name: 测试名称
        test_data: 测试数据
        container_config: 容器配置
        output_path: 可视化输出路径

    Returns:
        Dict[str, Any]: 测试结果
    """
    print(f"\n{'='*60}")
    print(f"运行测试：{test_name}")
    print(f"{'='*60}")

    # 数据准备
    if not validate_px_test_data(test_data):
        return {'success': False, 'error': '数据验证失败'}

    image_objects = convert_to_image_objects(test_data)
    print(f"✓ 数据准备：{len(image_objects)} 张图片")

    # 创建装箱器
    packer = create_rectpack_packer(container_config, 'AREA', True)
    if packer is None:
        return {'success': False, 'error': '装箱器创建失败'}

    print(f"✓ 装箱器创建成功")

    # 执行装箱
    start_time = time.time()
    placed_images, unplaced_images = place_images_single_container(
        packer, image_objects, container_config, container_config['spacing']
    )
    processing_time = time.time() - start_time

    print(f"✓ 图片放置：{len(placed_images)}/{len(image_objects)} 张图片")
    print(f"✓ 处理时间：{processing_time:.3f}秒")

    # 调整容器高度并计算统计
    container_data = adjust_container_height(placed_images, container_config)

    # 显示结果
    print(f"\n测试结果：")
    print(f"  成功率：{len(placed_images)}/{len(image_objects)} ({len(placed_images)/len(image_objects)*100:.1f}%)")
    print(f"  画布尺寸：{container_data['container_config']['actual_width']}x{container_data['actual_height']} px")
    print(f"  利用率：{container_data['utilization_rate']:.2f}%")
    print(f"  旋转图片：{sum(1 for img in placed_images if img.get('rotated', False))} 张")
    print(f"  处理速度：{len(image_objects)/processing_time:.1f} 图片/秒")

    # 创建可视化
    if output_path:
        success = create_matplotlib_visualization(container_data, output_path, f"{test_name} - RectPack算法布局结果")
        if success:
            print(f"✓ 可视化已保存：{output_path}")

    return {
        'success': True,
        'test_name': test_name,
        'total_images': len(image_objects),
        'placed_images': len(placed_images),
        'success_rate': len(placed_images)/len(image_objects)*100,
        'utilization_rate': container_data['utilization_rate'],
        'rotated_count': sum(1 for img in placed_images if img.get('rotated', False)),
        'processing_time': processing_time,
        'processing_speed': len(image_objects)/processing_time,
        'canvas_height': container_data['actual_height'],
        'container_data': container_data,
        'placed_images_data': placed_images
    }
