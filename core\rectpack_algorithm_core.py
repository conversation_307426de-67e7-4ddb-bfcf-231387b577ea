#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法统一核心模块
确保测试模式和生产模式使用完全一致的算法逻辑

模块化设计原则：
1. DRY原则：统一算法实现，避免重复
2. KISS原则：简单直接的算法逻辑
3. SOLID原则：单一职责，专注算法实现
4. YAGNI原则：只实现必要的算法功能
"""

import logging
import time
from typing import List, Dict, Any, Tuple, Optional

log = logging.getLogger(__name__)

class RectPackAlgorithmCore:
    """RectPack算法统一核心类"""
    
    def __init__(self, config_manager, unit_converter):
        self.config = config_manager.get_config()
        self.converter = unit_converter
        self.packer = None
        log.info("RectPack算法核心初始化完成")
    
    def initialize_packer(self, container_config) -> bool:
        """初始化装箱器"""
        try:
            from rectpack import newPacker
            
            # 使用统一配置创建装箱器
            packer_params = {
                'rotation': self.config.rotation_enabled
            }
            
            self.packer = newPacker(**packer_params)
            
            # 添加容器
            self.packer.add_bin(container_config.actual_width, container_config.max_height)
            
            log.info(f"装箱器初始化成功: {container_config.actual_width}x{container_config.max_height}px, "
                    f"旋转={'启用' if self.config.rotation_enabled else '禁用'}")
            return True
            
        except ImportError:
            log.error("RectPack库不可用")
            return False
        except Exception as e:
            log.error(f"装箱器初始化失败: {str(e)}")
            return False
    
    def prepare_images_for_packing(self, pattern_items: List[Dict[str, Any]], is_test_mode: bool = False) -> List[Dict[str, Any]]:
        """准备图片数据用于装箱"""
        images = []
        
        for i, item in enumerate(pattern_items):
            try:
                # 提取尺寸
                width_cm = item.get('width_cm', 0)
                height_cm = item.get('height_cm', 0)
                
                if width_cm <= 0 or height_cm <= 0:
                    log.warning(f"跳过无效尺寸的图片 {i+1}: {width_cm}x{height_cm}cm")
                    continue
                
                # 使用统一转换器转换尺寸
                width_px, height_px = self.converter.convert_image_size(width_cm, height_cm, is_test_mode)
                
                # 创建图片对象
                image_obj = {
                    'id': i + 1,
                    'width': width_px,
                    'height': height_px,
                    'width_cm': width_cm,
                    'height_cm': height_cm,
                    'name': item.get('pattern_name', f'Image_{i+1}'),
                    'path': item.get('path', ''),
                    'area': width_px * height_px,
                    'aspect_ratio': width_px / height_px if height_px > 0 else 0
                }
                
                # 处理数量
                quantity = max(1, item.get('quantity', 1))
                for q in range(quantity):
                    if quantity > 1:
                        image_copy = image_obj.copy()
                        image_copy['id'] = len(images) + 1
                        image_copy['name'] = f"{image_obj['name']}_copy{q+1}"
                        images.append(image_copy)
                    else:
                        images.append(image_obj)
                
            except Exception as e:
                log.error(f"处理第{i+1}个图片失败: {str(e)}")
                continue
        
        log.info(f"图片数据准备完成: {len(pattern_items)} -> {len(images)} 张图片")
        return images
    
    def pack_images_single_container(self, images: List[Dict[str, Any]], container_config) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """在单个容器中装箱图片"""
        if not self.packer:
            log.error("装箱器未初始化")
            return [], images
        
        placed_images = []
        unplaced_images = []
        
        try:
            # 添加所有矩形到装箱器
            for img in images:
                width_with_spacing = img['width'] + container_config.spacing
                height_with_spacing = img['height'] + container_config.spacing
                self.packer.add_rect(width_with_spacing, height_with_spacing, rid=img['id'])
            
            # 执行装箱
            start_time = time.time()
            self.packer.pack()
            pack_time = time.time() - start_time
            
            log.info(f"装箱计算完成，耗时：{pack_time:.3f}秒")
            
            # 获取放置结果
            placed_rects = self.packer.rect_list()
            placed_ids = set()
            
            for bin_id, x, y, width, height, rect_id in placed_rects:
                # 找到对应的图片
                original_img = next((img for img in images if img['id'] == rect_id), None)
                if original_img:
                    # 检查是否旋转
                    actual_width = width - container_config.spacing
                    actual_height = height - container_config.spacing
                    is_rotated = (actual_width != original_img['width'] or 
                                 actual_height != original_img['height'])
                    
                    placed_img = {
                        'id': rect_id,
                        'x': x,
                        'y': y,
                        'width': actual_width,
                        'height': actual_height,
                        'original_width': original_img['width'],
                        'original_height': original_img['height'],
                        'name': original_img['name'],
                        'path': original_img.get('path', ''),
                        'width_cm': original_img['width_cm'],
                        'height_cm': original_img['height_cm'],
                        'rotated': is_rotated,
                        'need_rotation': is_rotated,
                        'area': actual_width * actual_height
                    }
                    placed_images.append(placed_img)
                    placed_ids.add(rect_id)
            
            # 找出未放置的图片
            for img in images:
                if img['id'] not in placed_ids:
                    unplaced_images.append(img)
            
            log.info(f"单容器装箱完成: {len(placed_images)}/{len(images)} 张图片")
            
        except Exception as e:
            log.error(f"装箱过程出错：{str(e)}")
            unplaced_images = images.copy()
        
        return placed_images, unplaced_images
    
    def pack_images_multi_container(self, images: List[Dict[str, Any]], container_config) -> List[Dict[str, Any]]:
        """多容器装箱"""
        all_containers = []
        remaining_images = images.copy()
        container_index = 1
        
        while remaining_images:
            log.info(f"开始处理第{container_index}个容器，剩余图片: {len(remaining_images)}张")
            
            # 重新初始化装箱器
            if not self.initialize_packer(container_config):
                log.error("无法初始化装箱器，停止处理")
                break
            
            # 装箱当前容器
            placed_images, unplaced_images = self.pack_images_single_container(remaining_images, container_config)
            
            if placed_images:
                # 调整容器高度
                container_result = self.adjust_container_height(placed_images, container_config)
                all_containers.append(container_result)
                
                log.info(f"第{container_index}个容器完成，放置图片: {len(placed_images)}张")
            else:
                log.warning(f"第{container_index}个容器无法放置任何图片")
                break
            
            # 更新剩余图片
            remaining_images = unplaced_images
            container_index += 1
            
            # 防止无限循环
            if container_index > 100:
                log.error("容器数量超过限制，停止处理")
                break
        
        log.info(f"多容器装箱完成，总容器数: {len(all_containers)}")
        return all_containers
    
    def adjust_container_height(self, placed_images: List[Dict[str, Any]], container_config) -> Dict[str, Any]:
        """调整容器高度"""
        if not placed_images:
            return {
                'placed_images': [],
                'container_config': container_config,
                'actual_height': 0,
                'utilization_rate': 0.0,
                'used_area': 0,
                'container_area': 0
            }
        
        # 计算实际使用的画布高度
        max_y = max(img['y'] + img['height'] for img in placed_images)
        
        # 添加边距，但不能超过最大高度限制
        desired_height = max_y + container_config.spacing * 2
        actual_height = min(desired_height, container_config.max_height)
        actual_height = max(actual_height, 100)  # 确保最小高度
        
        # 计算利用率
        used_area = sum(img['width'] * img['height'] for img in placed_images)
        container_area = container_config.actual_width * actual_height
        utilization_rate = (used_area / container_area * 100) if container_area > 0 else 0
        
        log.info(f"容器高度调整: {container_config.max_height}px -> {actual_height}px, "
                f"利用率: {utilization_rate:.2f}%")
        
        return {
            'placed_images': placed_images,
            'container_config': container_config,
            'actual_height': actual_height,
            'utilization_rate': utilization_rate,
            'used_area': used_area,
            'container_area': container_area
        }


def create_algorithm_core(config_manager, unit_converter) -> RectPackAlgorithmCore:
    """创建算法核心实例"""
    return RectPackAlgorithmCore(config_manager, unit_converter)
